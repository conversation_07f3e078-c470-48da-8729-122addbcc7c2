import React, { useState, useEffect } from 'react';

export default function ScrollToTop() {
  const [isVisible, setIsVisible] = useState(false);

  // Show button when page is scrolled up to given distance
  const toggleVisibility = () => {
    if (window.pageYOffset > 300) {
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  };

  // Set the scroll event listener
  useEffect(() => {
    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  // Smooth scroll to top
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <>
      {isVisible && (
        <button
          className="naroop-scroll-to-top"
          onClick={scrollToTop}
          title="Back to top"
          aria-label="Scroll to top"
        >
          ⬆️
        </button>
      )}
    </>
  );
}

export function showToast(message, type = 'success') {
  // Create toast element
  const toast = document.createElement('div');
  toast.className = `naroop-toast naroop-toast-${type}`;
  toast.textContent = message;
  
  // Add to page
  document.body.appendChild(toast);
  
  // Animate in
  setTimeout(() => toast.classList.add('naroop-toast-show'), 100);
  
  // Remove after 3 seconds
  setTimeout(() => {
    toast.classList.remove('naroop-toast-show');
    setTimeout(() => document.body.removeChild(toast), 300);
  }, 3000);
}
