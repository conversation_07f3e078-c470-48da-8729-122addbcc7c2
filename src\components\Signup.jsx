import React, { useRef, useState } from 'react';
import { useAuth } from '../AuthContext';

export default function Signup({ onSwitchToLogin }) {
  const emailRef = useRef();
  const passwordRef = useRef();
  const displayNameRef = useRef();
  const avatarRef = useRef();
  const roleModelRef = useRef();
  const traditionRef = useRef();
  const dreamRef = useRef();
  const bioRef = useRef();
  const { signup } = useAuth();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  async function handleSubmit(e) {
    e.preventDefault();
    setError('');
    setLoading(true);
    try {
      await signup(
        emailRef.current.value,
        passwordRef.current.value,
        {
          name: displayNameRef.current.value,
          avatar: avatarRef.current.value || '👤',
          roleModel: roleModelRef.current.value,
          tradition: traditionRef.current.value,
          dream: dreamRef.current.value,
          bio: bioRef.current.value
        }
      );
    } catch (err) {
      setError('Failed to create an account.');
    }
    setLoading(false);
  }

  return (
    <div className="auth-form">
      <h2>Sign Up</h2>
      {error && <div className="auth-error">{error}</div>}
      <form onSubmit={handleSubmit}>
        <input type="email" ref={emailRef} placeholder="Email" required />
        <input type="password" ref={passwordRef} placeholder="Password" required minLength={6} />
        <input type="text" ref={displayNameRef} placeholder="Display Name (required)" required maxLength={30} />
        <input type="text" ref={avatarRef} placeholder="Avatar Emoji (e.g. 👩🏾‍💻)" maxLength={2} />
        <input type="text" ref={roleModelRef} placeholder="Favorite Black historical figure or role model (optional)" maxLength={40} />
        <input type="text" ref={traditionRef} placeholder="A tradition or value you cherish (optional)" maxLength={60} />
        <input type="text" ref={dreamRef} placeholder="A dream or goal you're working toward (optional)" maxLength={60} />
        <textarea ref={bioRef} placeholder="Short bio/introduction (optional)" maxLength={120} />
        <button disabled={loading} type="submit">Sign Up</button>
      </form>
      <p>Already have an account? <button onClick={onSwitchToLogin}>Log In</button></p>
    </div>
  );
}
