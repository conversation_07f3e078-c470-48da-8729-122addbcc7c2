/* Auth form styles for NAROOP */
.auth-form {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  max-width: 350px;
  margin: 60px auto 0 auto;
  padding: 2rem 2rem 1.5rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.auth-form h2 {
  color: #22223b;
  margin-bottom: 1.2rem;
}
.auth-form input {
  width: 100%;
  padding: 0.7rem 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f8fa;
  transition: border 0.2s;
}
.auth-form input:focus {
  border: 1.5px solid #fbbf24;
  outline: none;
}
.auth-form button[type="submit"] {
  width: 100%;
  background: #22223b;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.7rem 0;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  margin-bottom: 0.5rem;
  transition: background 0.2s;
}
.auth-form button[type="submit"]:hover {
  background: #fbbf24;
  color: #22223b;
}
.auth-form p {
  margin-top: 0.5rem;
  font-size: 0.95rem;
}
.auth-form .auth-error {
  color: #e63946;
  background: #fff0f3;
  border-radius: 8px;
  padding: 0.5rem 1rem;
  margin-bottom: 1rem;
  width: 100%;
  text-align: center;
}
.auth-form button {
  background: none;
  border: none;
  color: #3a86ff;
  cursor: pointer;
  font-size: 1rem;
  text-decoration: underline;
  padding: 0;
}
.auth-form button:focus {
  outline: 2px solid #fbbf24;
}

.naroop-auth-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem 0 2rem;
}
.naroop-logout-btn {
  background: #e63946;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 0.5rem 1.2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.naroop-logout-btn:hover {
  background: #fbbf24;
  color: #22223b;
}
