import React, { useState } from 'react';

export default function TagInput({ tags, onChange, placeholder = "Add tags..." }) {
  const [inputValue, setInputValue] = useState('');

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const handleInputKeyPress = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addTag();
    }
  };

  const addTag = () => {
    const newTag = inputValue.trim().toLowerCase();
    if (newTag && !tags.includes(newTag) && tags.length < 10) {
      onChange([...tags, newTag]);
      setInputValue('');
    }
  };

  const removeTag = (tagToRemove) => {
    onChange(tags.filter(tag => tag !== tagToRemove));
  };

  return (
    <div className="naroop-tag-input">
      <div className="naroop-tags-display">
        {tags.map(tag => (
          <span key={tag} className="naroop-tag-item">
            #{tag}
            <button 
              type="button" 
              onClick={() => removeTag(tag)}
              className="naroop-tag-remove"
              title="Remove tag"
            >
              ×
            </button>
          </span>
        ))}
      </div>
      <input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        onKeyPress={handleInputKeyPress}
        onBlur={addTag}
        placeholder={tags.length < 10 ? placeholder : "Maximum 10 tags"}
        className="naroop-tag-input-field"
        maxLength={30}
        disabled={tags.length >= 10}
      />
      <small className="naroop-tag-help">
        Press Enter or comma to add tags. Max 10 tags.
      </small>
    </div>
  );
}
