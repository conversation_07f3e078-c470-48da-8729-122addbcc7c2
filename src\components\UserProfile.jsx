import React, { useState } from 'react';

export default function UserProfile({ user = {}, stories = [], onClose, onProfileUpdate }) {
  const [editing, setEditing] = useState(false);
  const [name, setName] = useState(user.name || user.email || 'User');
  const [avatar, setAvatar] = useState(user.avatar || '👤');
  const [bio, setBio] = useState(user.bio || '');
  const [roleModel, setRoleModel] = useState(user.roleModel || '');
  const [tradition, setTradition] = useState(user.tradition || '');
  const [dream, setDream] = useState(user.dream || '');
  const [saving, setSaving] = useState(false);
  const userStories = Array.isArray(stories) ? stories.filter(story => story.author === user.id) : [];
  const totalReactions = userStories.reduce((sum, story) => sum + (story.hearts || 0) + (story.claps || 0), 0);

  const handleEdit = () => setEditing(true);
  const handleCancel = () => setEditing(false);

  const handleSave = async () => {
    setSaving(true);
    if (onProfileUpdate) await onProfileUpdate({ name, avatar, bio, roleModel, tradition, dream });
    setSaving(false);
    setEditing(false);
  };

  return (
    <div className="naroop-user-profile-modal">
      <div className="naroop-user-profile-content">
        <div className="naroop-profile-header">
          {onClose && <button className="naroop-close-profile" onClick={onClose}>×</button>}
          <div className="naroop-profile-avatar" aria-label="User avatar">{avatar}</div>
          {editing ? (
            <>
              <input
                type="text"
                value={name}
                onChange={e => setName(e.target.value)}
                aria-label="Display Name"
                className="naroop-profile-edit-input"
                maxLength={30}
              />
              <input
                type="text"
                value={avatar}
                onChange={e => setAvatar(e.target.value)}
                aria-label="Avatar Emoji"
                className="naroop-profile-edit-input"
                maxLength={2}
              />
              <input
                type="text"
                value={roleModel}
                onChange={e => setRoleModel(e.target.value)}
                aria-label="Favorite Black historical figure or role model"
                className="naroop-profile-edit-input"
                placeholder="Favorite Black historical figure or role model"
                maxLength={40}
              />
              <input
                type="text"
                value={tradition}
                onChange={e => setTradition(e.target.value)}
                aria-label="A tradition or value you cherish"
                className="naroop-profile-edit-input"
                placeholder="A tradition or value you cherish"
                maxLength={60}
              />
              <input
                type="text"
                value={dream}
                onChange={e => setDream(e.target.value)}
                aria-label="A dream or goal you're working toward"
                className="naroop-profile-edit-input"
                placeholder="A dream or goal you're working toward"
                maxLength={60}
              />
              <textarea
                value={bio}
                onChange={e => setBio(e.target.value)}
                aria-label="Bio"
                className="naroop-profile-edit-textarea"
                maxLength={200}
                placeholder="Short bio/introduction"
              />
              <div className="naroop-profile-edit-actions">
                <button onClick={handleSave} disabled={saving} className="naroop-save-btn">{saving ? 'Saving...' : 'Save'}</button>
                <button onClick={handleCancel} className="naroop-cancel-btn">Cancel</button>
              </div>
            </>
          ) : (
            <>
              <h3>{name}</h3>
              {bio && <p className="naroop-profile-bio">{bio}</p>}
              {roleModel && <p><strong>Role Model:</strong> {roleModel}</p>}
              {tradition && <p><strong>Tradition/Value:</strong> {tradition}</p>}
              {dream && <p><strong>Dream/Goal:</strong> {dream}</p>}
              <p className="naroop-profile-joined">Joined {user.joinDate ? new Date(user.joinDate).toLocaleDateString() : 'N/A'}</p>
              <button onClick={handleEdit} className="naroop-edit-profile-btn">Edit Profile</button>
            </>
          )}
        </div>
        
        <div className="naroop-profile-stats">
          <div className="naroop-profile-stat">
            <div className="naroop-stat-number">{userStories.length}</div>
            <div className="naroop-stat-label">Stories Shared</div>
          </div>
          <div className="naroop-profile-stat">
            <div className="naroop-stat-number">{totalReactions}</div>
            <div className="naroop-stat-label">Total Reactions</div>
          </div>
          <div className="naroop-profile-stat">
            <div className="naroop-stat-number">{user.badges ? user.badges.length : 0}</div>
            <div className="naroop-stat-label">Badges Earned</div>
          </div>
        </div>
        
        {user.badges && user.badges.length > 0 && (
          <div className="naroop-profile-badges">
            <h4>Badges</h4>
            <div className="naroop-badges-list">
              {user.badges.map(badge => (
                <span key={badge.name} className="naroop-badge">
                  {badge.emoji} {badge.name}
                </span>
              ))}
            </div>
          </div>
        )}
        
        <div className="naroop-profile-stories">
          <h4>Recent Stories ({userStories.length})</h4>
          {userStories.length === 0 && <p>No stories yet.</p>}
          {userStories.slice(0, 3).map(story => (
            <div key={story.id} className="naroop-profile-story-item">
              <div className="naroop-profile-story-meta">
                <span className="naroop-story-topic">{story.topic}</span>
                <span>{story.timestamp}</span>
              </div>
              <h5>{story.title}</h5>
              <p>{story.content ? story.content.substring(0, 100) : ''}...</p>
              <div className="naroop-profile-story-reactions">
                ❤️ {story.hearts || 0} 👏 {story.claps || 0} 📤 {story.shares || 0}
              </div>
            </div>
          ))}
        </div>
        
        {/* Debug info for development */}
        <pre style={{ background: '#f9f9f9', color: '#333', fontSize: 12, padding: 8, borderRadius: 4 }}>
          {JSON.stringify(user, null, 2)}
        </pre>
      </div>
    </div>
  );
}
