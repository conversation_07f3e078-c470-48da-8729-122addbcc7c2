import React, { useRef, useState } from 'react';
import { useAuth } from '../AuthContext';

export default function PasswordReset({ onBackToLogin }) {
  const emailRef = useRef();
  const { resetPassword } = useAuth();
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  async function handleSubmit(e) {
    e.preventDefault();
    setMessage('');
    setError('');
    setLoading(true);
    try {
      await resetPassword(emailRef.current.value);
      setMessage('Check your inbox for a password reset link.');
    } catch (err) {
      setError('Failed to send reset email.');
    }
    setLoading(false);
  }

  return (
    <div className="auth-form">
      <h2>Reset Password</h2>
      {message && <div style={{ color: '#38b000', marginBottom: '1rem' }}>{message}</div>}
      {error && <div className="auth-error">{error}</div>}
      <form onSubmit={handleSubmit}>
        <input type="email" ref={emailRef} placeholder="Email" required />
        <button disabled={loading} type="submit">Send Reset Link</button>
      </form>
      <p><button onClick={onBackToLogin}>Back to Login</button></p>
    </div>
  );
}
