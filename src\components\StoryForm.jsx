import React, { useState } from 'react';
import TagInput from './TagInput';

const TOPICS = [
  'Culture & Heritage',
  'Family & Community',
  'Art & Creativity',
  'History & Legacy',
  'Education & Achievement',
  'Business & Entrepreneurship',
  'Health & Wellness',
  'Inspiration & Overcoming',
  'Activism & Leadership',
  'Joy & Celebration',
];

const TOPIC_EMOJIS = {
  'Culture & Heritage': '🏛️',
  'Family & Community': '👨‍👩‍👧‍👦',
  'Art & Creativity': '🎨',
  'History & Legacy': '📚',
  'Education & Achievement': '🎓',
  'Business & Entrepreneurship': '💼',
  'Health & Wellness': '💪',
  'Inspiration & Overcoming': '⭐',
  'Activism & Leadership': '✊',
  'Joy & Celebration': '🎉',
};

export default function StoryForm({ onSubmit, isSubmitting }) {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [topic, setTopic] = useState(TOPICS[0]);
  const [image, setImage] = useState(null);
  const [tags, setTags] = useState([]);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  function validateForm() {
    const newErrors = {};
    
    if (!title.trim()) {
      newErrors.title = 'Title is required';
    } else if (title.trim().length < 3) {
      newErrors.title = 'Title must be at least 3 characters';
    }
    
    if (!content.trim()) {
      newErrors.content = 'Your story is required';
    } else if (content.trim().length < 10) {
      newErrors.content = 'Story must be at least 10 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  function handleBlur(field) {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateForm();
  }

  function handleImageChange(e) {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setErrors(prev => ({ ...prev, image: 'Image must be less than 5MB' }));
        return;
      }
      setImage(file);
      setErrors(prev => ({ ...prev, image: null }));
    }
  }

  function handleSubmit(e) {
    e.preventDefault();
    setTouched({ title: true, content: true });
    
    if (validateForm()) {
      onSubmit({ title: title.trim(), content: content.trim(), topic, image, tags });
      setTitle('');
      setContent('');
      setTopic(TOPICS[0]);
      setImage(null);
      setTags([]);
      setErrors({});
      setTouched({});
      
      // Reset file input
      const fileInput = e.target.querySelector('input[type="file"]');
      if (fileInput) fileInput.value = '';
    }
  }

  const getFieldClassName = (field, baseClass) => {
    let className = baseClass;
    if (touched[field] && errors[field]) {
      className += ' error';
    } else if (touched[field] && !errors[field]) {
      className += ' success';
    }
    return className;
  };

  return (
    <form className="naroop-story-form" onSubmit={handleSubmit}>
      <h4>✍️ Share Your Story</h4>
      
      <label>
        Story Title
        <input 
          type="text"
          value={title} 
          onChange={e => setTitle(e.target.value)}
          onBlur={() => handleBlur('title')}
          className={getFieldClassName('title', '')}
          placeholder="Give your story a meaningful title..."
          required 
          disabled={isSubmitting}
        />
        {touched.title && errors.title && (
          <span className="error-message">{errors.title}</span>
        )}
        {touched.title && !errors.title && title.trim() && (
          <span className="success-message">✓ Great title!</span>
        )}
      </label>
      
      <label>
        Topic Category
        <select 
          value={topic} 
          onChange={e => setTopic(e.target.value)}
          disabled={isSubmitting}
        >
          {TOPICS.map(t => (
            <option key={t} value={t}>
              {TOPIC_EMOJIS[t]} {t}
            </option>
          ))}
        </select>
      </label>
      
      <label>
        Your Story
        <textarea 
          value={content} 
          onChange={e => setContent(e.target.value)}
          onBlur={() => handleBlur('content')}
          className={getFieldClassName('content', '')}
          placeholder="Share your experience, wisdom, or inspiration..."
          required 
          rows={5}
          disabled={isSubmitting}
        />
        <div className="character-count">
          {content.length}/2000 characters
        </div>
        {touched.content && errors.content && (
          <span className="error-message">{errors.content}</span>
        )}
        {touched.content && !errors.content && content.trim() && (
          <span className="success-message">✓ Beautiful story!</span>
        )}
      </label>
      
      <label>
        🏷️ Tags (optional)
        <TagInput 
          tags={tags}
          onChange={setTags}
          placeholder="Add relevant tags (press Enter or comma)..."
        />
        <small>Help others discover your story with relevant tags</small>
      </label>
      
      <label>
        📷 Add an Image (optional)
        <input 
          type="file" 
          accept="image/*" 
          onChange={handleImageChange}
          disabled={isSubmitting}
        />
        <small>Maximum file size: 5MB</small>
        {errors.image && (
          <span className="error-message">{errors.image}</span>
        )}
        {image && !errors.image && (
          <span className="success-message">✓ Image ready to upload!</span>
        )}
      </label>
      
      <button type="submit" disabled={isSubmitting || Object.keys(errors).length > 0}>
        {isSubmitting ? (
          <>
            <span className="loading-spinner"></span>
            Sharing Your Story...
          </>
        ) : (
          '✨ Share My Story'
        )}
      </button>
    </form>
  );
}
