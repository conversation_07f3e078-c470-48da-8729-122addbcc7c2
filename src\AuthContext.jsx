import React, { createContext, useContext, useEffect, useState } from 'react';
import { auth, db } from './firebase';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  sendPasswordResetEmail
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs } from 'firebase/firestore';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  function signup(email, password, profile = {}) {
    return createUserWithEmailAndPassword(auth, email, password)
      .then(async (cred) => {
        // Create user profile in Firestore with extra fields
        await setDoc(doc(db, 'users', cred.user.uid), {
          email: cred.user.email,
          name: profile.name || '',
          avatar: profile.avatar || '👤',
          joinDate: new Date().toISOString(),
          totalStories: 0,
          totalReactions: 0,
          badges: [],
          roleModel: profile.roleModel || '',
          tradition: profile.tradition || '',
          dream: profile.dream || '',
          bio: profile.bio || '',
          // New fields for richer engagement
          skills: profile.skills || [],
          interests: profile.interests || [],
          goals: profile.goals || '',
          mentorAvailable: profile.mentorAvailable || false
        });
        return cred;
      });
  }

  function login(email, password) {
    return signInWithEmailAndPassword(auth, email, password);
  }

  function logout() {
    return signOut(auth);
  }

  function resetPassword(email) {
    return sendPasswordResetEmail(auth, email);
  }

  async function fetchUserProfile(uid) {
    const ref = doc(db, 'users', uid)
    const snap = await getDoc(ref)
    return snap.exists() ? snap.data() : null
  }

  // New community connection functions
  async function sendConnectionRequest(fromUserId, toUserId, message = '') {
    const connectionRef = doc(db, 'connections', `${fromUserId}_${toUserId}`);
    await setDoc(connectionRef, {
      fromUserId,
      toUserId,
      message,
      status: 'pending',
      createdAt: new Date().toISOString(),
      type: 'connection'
    });
  }

  async function acceptConnectionRequest(connectionId) {
    const connectionRef = doc(db, 'connections', connectionId);
    await updateDoc(connectionRef, {
      status: 'accepted',
      acceptedAt: new Date().toISOString()
    });
  }

  async function findMentors(skills, location = null) {
    // Implementation would query users with mentorAvailable: true
    // and matching skills/location
    const mentorsQuery = query(
      collection(db, 'users'),
      where('mentorAvailable', '==', true),
      where('skills', 'array-contains-any', skills)
    );
    const snapshot = await getDocs(mentorsQuery);
    return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async user => {
      setCurrentUser(user);
      setLoading(false);
    });
    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    signup,
    login,
    logout,
    resetPassword,
    fetchUserProfile,
    sendConnectionRequest,
    acceptConnectionRequest,
    findMentors
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}

export { AuthContext };
