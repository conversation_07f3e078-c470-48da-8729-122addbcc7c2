import React, { createContext, useContext, useEffect, useState } from 'react';
import { auth, db } from './firebase';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  sendPasswordResetEmail
} from 'firebase/auth';
import { doc, setDoc, getDoc } from 'firebase/firestore';

const AuthContext = createContext();

export function useAuth() {
  return useContext(AuthContext);
}

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  function signup(email, password, profile = {}) {
    return createUserWithEmailAndPassword(auth, email, password)
      .then(async (cred) => {
        // Create user profile in Firestore with extra fields
        await setDoc(doc(db, 'users', cred.user.uid), {
          email: cred.user.email,
          name: profile.name || '',
          avatar: profile.avatar || '👤',
          joinDate: new Date().toISOString(),
          totalStories: 0,
          totalReactions: 0,
          badges: [],
          roleModel: profile.roleModel || '',
          tradition: profile.tradition || '',
          dream: profile.dream || '',
          bio: profile.bio || '',
          // New fields for richer engagement
          skills: profile.skills || [],
          interests: profile.interests || [],
          goals: profile.goals || '',
          mentorAvailable: profile.mentorAvailable || false
        });
        return cred;
      });
  }

  function login(email, password) {
    return signInWithEmailAndPassword(auth, email, password);
  }

  function logout() {
    return signOut(auth);
  }

  function resetPassword(email) {
    return sendPasswordResetEmail(auth, email);
  }

  async function fetchUserProfile(uid) {
    const ref = doc(db, 'users', uid)
    const snap = await getDoc(ref)
    return snap.exists() ? snap.data() : null
  }

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async user => {
      setCurrentUser(user);
      setLoading(false);
    });
    return unsubscribe;
  }, []);

  const value = {
    currentUser,
    signup,
    login,
    logout,
    resetPassword,
    fetchUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}

export { AuthContext };
