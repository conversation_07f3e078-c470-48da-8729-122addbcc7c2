import React, { useContext, useState, useEffect } from 'react';
import { AuthContext } from '../AuthContext';
import UserProfile from './UserProfile';
import ProgressTracker from './ProgressTracker';
import CommunityStats from './CommunityStats';
import { auth, db } from '../firebase';
import { doc, updateDoc, getDoc, setDoc } from 'firebase/firestore';

const Account = () => {
  const { currentUser } = useContext(AuthContext);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProfile = async () => {
      if (currentUser) {
        try {
          console.log('Current user:', currentUser);
          const ref = doc(db, 'users', currentUser.uid);
          const snap = await getDoc(ref);
          if (snap.exists()) {
            console.log('Profile found:', snap.data());
            setUserProfile({ ...snap.data(), id: currentUser.uid });
          } else {
            // Create a default profile if missing
            const defaultProfile = {
              email: currentUser.email,
              name: currentUser.displayName || '',
              avatar: '👤',
              joinDate: new Date().toISOString(),
              totalStories: 0,
              totalReactions: 0,
              badges: [],
              bio: '',
              roleModel: '',
              tradition: '',
              dream: ''
            };
            await setDoc(ref, defaultProfile);
            console.log('Default profile created:', defaultProfile);
            setUserProfile({ ...defaultProfile, id: currentUser.uid });
          }
        } catch (err) {
          setError('Failed to load profile: ' + err.message);
          console.error('Profile fetch error:', err);
        }
      } else {
        console.log('No currentUser');
      }
      setLoading(false);
    };
    fetchProfile();
  }, [currentUser]);

  const handleProfileUpdate = async (updates) => {
    if (!currentUser) return;
    const ref = doc(db, 'users', currentUser.uid);
    await updateDoc(ref, updates);
    setUserProfile(prev => ({ ...prev, ...updates }));
  };

  const handleLogout = async () => {
    await auth.signOut();
    // Optionally redirect or show a message
  };

  if (!currentUser) {
    return <div className="account-section">Please log in to view your account.</div>;
  }
  if (loading) {
    return <div className="account-section">
      Loading your profile...
      <pre style={{fontSize:12, color:'#888'}}>
        currentUser: {JSON.stringify(currentUser, null, 2)}
        userProfile: {JSON.stringify(userProfile, null, 2)}
        error: {error}
      </pre>
    </div>;
  }
  if (error) {
    return <div className="account-section">{error}</div>;
  }

  return (
    <main className="account-section" aria-label="User Account Section">
      <section className="account-header">
        <h1 className="account-welcome">Welcome, {userProfile.name || currentUser.displayName || currentUser.email}!</h1>
        <button onClick={handleLogout} className="logout-btn" aria-label="Log out">Log Out</button>
        <a href="/" className="account-home-link" aria-label="Back to Home">← Back to Home</a>
      </section>
      <section className="account-dashboard">
        <div className="account-profile">
          <UserProfile user={userProfile} onProfileUpdate={handleProfileUpdate} />
        </div>
        <div className="account-progress">
          <ProgressTracker userId={currentUser.uid} />
        </div>
        <div className="account-community">
          <CommunityStats userId={currentUser.uid} />
        </div>
      </section>
      {/* Future: Add story management, profile editing, and more features here */}
    </main>
  );
};

export default Account;
