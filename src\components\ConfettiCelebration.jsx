import React, { useEffect, useState } from 'react';

export default function ConfettiCelebration({ trigger, message = "🎉 Congratulations! 🎉" }) {
  const [showConfetti, setShow<PERSON>onfetti] = useState(false);

  useEffect(() => {
    if (trigger) {
      setShowConfetti(true);
      const timer = setTimeout(() => setShow<PERSON>on<PERSON>tti(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [trigger]);

  if (!showConfetti) return null;

  return (
    <div className="naroop-confetti-overlay">
      <div className="naroop-confetti-message">
        {message}
      </div>
      <div className="naroop-confetti-container">
        {[...Array(50)].map((_, i) => (
          <div
            key={i}
            className="naroop-confetti-piece"
            style={{
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              backgroundColor: ['#e63946', '#fbbf24', '#10b981', '#3b82f6', '#8b5cf6'][Math.floor(Math.random() * 5)]
            }}
          />
        ))}
      </div>
    </div>
  );
}
