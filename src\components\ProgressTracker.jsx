import React, { useState } from 'react';

export default function ProgressTracker({ stories, currentUser, userProfiles }) {
  const [showDetails, setShowDetails] = useState(false);
  
  const userStories = stories.filter(story => story.author === currentUser);
  const totalWords = userStories.reduce((sum, story) => sum + story.content.split(' ').length, 0);
  const totalReactions = userStories.reduce((sum, story) => sum + story.hearts + story.claps, 0);
  const averageReactions = userStories.length > 0 ? Math.round(totalReactions / userStories.length) : 0;
  
  // Calculate achievements
  const achievements = [
    {
      name: "First Story",
      emoji: "🌟",
      description: "Share your first story",
      completed: userStories.length >= 1,
      requirement: 1
    },
    {
      name: "Story Teller",
      emoji: "📖",
      description: "Share 5 stories",
      completed: userStories.length >= 5,
      requirement: 5,
      current: userStories.length
    },
    {
      name: "Beloved Writer",
      emoji: "❤️",
      description: "Receive 50 total reactions",
      completed: totalReactions >= 50,
      requirement: 50,
      current: totalReactions
    },
    {
      name: "<PERSON> Weaver",
      emoji: "✍️",
      description: "Write 1000+ words total",
      completed: totalWords >= 1000,
      requirement: 1000,
      current: totalWords
    },
    {
      name: "Community Favorite",
      emoji: "🏆",
      description: "Average 10+ reactions per story",
      completed: averageReactions >= 10,
      requirement: 10,
      current: averageReactions
    }
  ];

  const completedAchievements = achievements.filter(a => a.completed);
  const nextAchievement = achievements.find(a => !a.completed);

  return (
    <div className="naroop-progress-tracker">
      <div className="naroop-progress-header">
        <h4>📈 Your Journey</h4>
        <button 
          className="naroop-toggle-details"
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? '▼' : '▶'}
        </button>
      </div>
      
      <div className="naroop-progress-summary">
        <div className="naroop-progress-item">
          <span className="naroop-progress-number">{userStories.length}</span>
          <span className="naroop-progress-label">Stories</span>
        </div>
        <div className="naroop-progress-item">
          <span className="naroop-progress-number">{totalReactions}</span>
          <span className="naroop-progress-label">Reactions</span>
        </div>
        <div className="naroop-progress-item">
          <span className="naroop-progress-number">{completedAchievements.length}/{achievements.length}</span>
          <span className="naroop-progress-label">Achievements</span>
        </div>
      </div>

      {showDetails && (
        <div className="naroop-progress-details">
          {nextAchievement && (
            <div className="naroop-next-achievement">
              <h5>🎯 Next Achievement</h5>
              <div className="naroop-achievement-card">
                <span className="naroop-achievement-emoji">{nextAchievement.emoji}</span>
                <div>
                  <div className="naroop-achievement-name">{nextAchievement.name}</div>
                  <div className="naroop-achievement-desc">{nextAchievement.description}</div>
                  {nextAchievement.current !== undefined && (
                    <div className="naroop-achievement-progress">
                      <div className="naroop-progress-bar">
                        <div 
                          className="naroop-progress-fill"
                          style={{ width: `${Math.min((nextAchievement.current / nextAchievement.requirement) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <span className="naroop-progress-text">
                        {nextAchievement.current} / {nextAchievement.requirement}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="naroop-achievements-grid">
            <h5>🏅 Achievements</h5>
            {achievements.map(achievement => (
              <div 
                key={achievement.name}
                className={`naroop-achievement-badge ${achievement.completed ? 'completed' : 'locked'}`}
              >
                <span className="naroop-achievement-emoji">{achievement.emoji}</span>
                <span className="naroop-achievement-name">{achievement.name}</span>
                {achievement.completed && <span className="naroop-achievement-check">✓</span>}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
