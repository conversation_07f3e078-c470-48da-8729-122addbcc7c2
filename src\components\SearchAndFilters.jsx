import React from 'react';

export default function SearchAndFilters({ 
  searchQuery, 
  onSearchChange, 
  selectedTopic, 
  onTopicChange, 
  sortBy, 
  onSortChange,
  showBookmarksOnly,
  onBookmarksToggle,
  bookmarkCount,
  TOPICS,
  stories = []
}) {
  // Get popular tags from stories
  const getPopularTags = () => {
    const tagCounts = {};
    stories.forEach(story => {
      if (story.tags) {
        story.tags.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      }
    });
    
    return Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 8)
      .map(([tag, count]) => ({ tag, count }));
  };

  const popularTags = getPopularTags();
  return (
    <div className="naroop-search-filters">
      {/* Search Bar */}
      <div className="naroop-search-container">
        <div className="naroop-search-box">
          <input
            type="text"
            placeholder="🔍 Search stories, topics, or keywords..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="naroop-search-input"
          />
          {searchQuery && (
            <button 
              className="naroop-clear-search"
              onClick={() => onSearchChange('')}
              title="Clear search"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      {/* Filters Row */}
      <div className="naroop-filters-row">
        {/* Topic Filter */}
        <div className="naroop-filter-group">
          <label htmlFor="topic-select">📂 Topic:</label>
          <select 
            id="topic-select"
            value={selectedTopic} 
            onChange={(e) => onTopicChange(e.target.value)}
            className="naroop-filter-select"
          >
            {TOPICS.map(topic => (
              <option key={topic} value={topic}>{topic}</option>
            ))}
          </select>
        </div>

        {/* Sort Filter */}
        <div className="naroop-filter-group">
          <label htmlFor="sort-select">📊 Sort by:</label>
          <select 
            id="sort-select"
            value={sortBy} 
            onChange={(e) => onSortChange(e.target.value)}
            className="naroop-filter-select"
          >
            <option value="newest">🕐 Newest</option>
            <option value="oldest">🕑 Oldest</option>
            <option value="mostLoved">❤️ Most Loved</option>
            <option value="mostShared">📤 Most Shared</option>
            <option value="monthlyVotes">🗳️ Monthly Votes</option>
          </select>
        </div>

        {/* Bookmarks Toggle */}
        <div className="naroop-filter-group">
          <button 
            className={`naroop-bookmarks-toggle ${showBookmarksOnly ? 'active' : ''}`}
            onClick={onBookmarksToggle}
            title="Show bookmarked stories only"
          >
            📚 Bookmarks {bookmarkCount > 0 && `(${bookmarkCount})`}
          </button>
        </div>
      </div>
      
      {/* Popular Tags */}
      {popularTags.length > 0 && (
        <div className="naroop-popular-tags">
          <h4>🏷️ Popular Tags</h4>
          <div className="naroop-tags-container">
            {popularTags.map(({ tag, count }) => (
              <button
                key={tag}
                className="naroop-popular-tag"
                onClick={() => onSearchChange(`#${tag}`)}
                title={`${count} stories tagged with ${tag}`}
              >
                #{tag} ({count})
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
