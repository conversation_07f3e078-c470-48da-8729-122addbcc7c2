import React from 'react';

export default function StoryOfTheMonth({ story, votingPeriod, onVote }) {
  if (!story) return null;

  return (
    <section className="naroop-story-of-month">
      <div className="naroop-month-container">
        <div className="naroop-month-header">
          <h3>🏆 Story of the Month</h3>
          <div className="naroop-voting-period">{votingPeriod}</div>
        </div>
        
        <div className="naroop-month-content">
          <div className="naroop-month-info">
            <div className="naroop-month-badge">
              <span className="naroop-crown">👑</span>
              <div className="naroop-votes-count">{story.monthlyVotes} votes</div>
            </div>
            <span className="naroop-story-topic">{story.topic}</span>
            <h4>{story.title}</h4>
            <p>{story.content.substring(0, 200)}...</p>
            
            <div className="naroop-month-stats">
              <span className="naroop-stat">❤️ {story.hearts}</span>
              <span className="naroop-stat">👏 {story.claps}</span>
              <span className="naroop-stat">📤 {story.shares}</span>
              <span className="naroop-stat">⏱️ {story.readingTime} min read</span>
            </div>
          </div>
          
          {story.imageUrl && (
            <div className="naroop-month-image">
              <img src={story.imageUrl} alt="Story of the Month" />
              <div className="naroop-winner-overlay">
                <span className="naroop-winner-text">WINNER</span>
              </div>
            </div>
          )}
        </div>
        
        <div className="naroop-month-footer">
          <p className="naroop-voting-info">
            🗳️ <strong>Community Choice:</strong> This story was chosen by our amazing community! 
            Keep voting for your favorites to see them featured here.
          </p>
        </div>
      </div>
    </section>
  );
}
